#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Table formatting service for PDF generation
"""

from typing import List, Tuple, Optional
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle, Paragraph
from reportlab.lib.styles import ParagraphStyle
from reportlab.pdfbase import pdfmetrics

class TableFormatter:
    def __init__(self, page_size: Tuple[float, float], is_relatorio_separacao: bool = True):
        self.page_size = page_size
        self.is_relatorio_separacao = is_relatorio_separacao
        self.available_width = page_size[0] - 30
        self.available_height = page_size[1] - 40
        self.base_style = TableStyle([
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 5),
            ('RIGHTPADDING', (0, 0), (-1, -1), 5),
        ])

    def create_styles(self, font_size: float, header_font_size: Optional[float] = None) -> Tuple[ParagraphStyle, ParagraphStyle, ParagraphStyle]:
        """Create paragraph styles for cells and headers"""
        if header_font_size is None:
            header_font_size = font_size

        cell_style_wrap = ParagraphStyle(
            name="cell_wrap",
            fontName="Helvetica",
            fontSize=font_size,
            alignment=1,
            leading=font_size * 1.2,
        )
        
        cell_style_nowrap = ParagraphStyle(
            name="cell_nowrap",
            fontName="Helvetica",
            fontSize=font_size,
            alignment=1,
            leading=font_size * 1.2,
            wordWrap='CJK'
        )
        
        header_style = ParagraphStyle(
            name="header",
            fontName="Helvetica-Bold",
            fontSize=header_font_size,
            alignment=1,
            leading=header_font_size * 1.2,
        )
        
        return cell_style_wrap, cell_style_nowrap, header_style

    def calculate_column_widths(self, data: List[List[str]], font_size: float, header_font_size: float) -> List[float]:
        """Calculate optimal column widths based on content"""
        # Calculate content widths
        col_widths_content = [
            max(pdfmetrics.stringWidth(str(row[col]), 'Helvetica', font_size) for row in data[1:]) + 10
            for col in range(len(data[0]))
        ]
        
        # Calculate header widths
        col_widths_header = [
            pdfmetrics.stringWidth(str(data[0][col]), 'Helvetica-Bold', header_font_size) + 15
            for col in range(len(data[0]))
        ]
        
        # Use maximum between content and header widths
        col_widths_raw = [
            max(content, header)
            for content, header in zip(col_widths_content, col_widths_header)
        ]
        
        # Special handling for SKU and QNT columns
        for col in [0, 1]:
            col_widths_raw[col] = max(
                max(pdfmetrics.stringWidth(str(row[col]), 'Helvetica', font_size) for row in data) + 10,
                col_widths_header[col]
            )
        
        # Adjust widths to fit page
        return self._adjust_column_widths(col_widths_raw, col_widths_header)

    def _adjust_column_widths(self, raw_widths: List[float], min_widths: List[float]) -> List[float]:
        """Adjust column widths to fit page width while maintaining proportions"""
        col_widths = []
        space_used = 0
        
        # First, guarantee widths for SKU and QNT columns
        for col in [0, 1]:
            width = max(raw_widths[col], min_widths[col])
            col_widths.append(width)
            space_used += width
        
        # Distribute remaining space
        remaining_space = self.available_width - space_used
        remaining_widths = raw_widths[2:]
        remaining_total = sum(remaining_widths)
        
        # Calculate proportional widths for remaining columns
        for width in remaining_widths:
            prop_width = (width / remaining_total) * remaining_space
            col_widths.append(prop_width)
        
        return col_widths

    def create_table(self, data: List[List[str]], line_height_multiplier: float = 1.2,
                    max_font_size: int = 12, min_font_size: int = 8) -> Table:
        """Create a formatted table with optimal font sizes and column widths"""
        header_font_size = max_font_size  # Fixed header font size
        
        # Try different font sizes
        for font_size in range(max_font_size, min_font_size - 1, -1):
            cell_style_wrap, cell_style_nowrap, header_style = self.create_styles(font_size, header_font_size)
            col_widths = self.calculate_column_widths(data, font_size, header_font_size)
            
            # Process table data
            processed_data = self._process_table_data(data, col_widths, cell_style_wrap, 
                                                    cell_style_nowrap, header_style, font_size)
            
            # Calculate row heights
            row_heights = self._calculate_row_heights(processed_data, col_widths, line_height_multiplier)
            
            # Create table
            table = Table(processed_data, colWidths=col_widths, rowHeights=row_heights)
            table.setStyle(self.base_style)
            
            # Apply styles
            self._apply_table_styles(table, data, font_size, header_font_size)
            
            # Check if table fits
            w, h = table.wrapOn(None, self.page_size[0], self.page_size[1])
            if w <= self.available_width and h <= self.available_height:
                return table
        
        # If no size fits, return table with minimum font size
        return self._create_minimum_size_table(data, min_font_size, header_font_size, line_height_multiplier)

    def _process_table_data(self, data: List[List[str]], col_widths: List[float],
                          cell_style_wrap: ParagraphStyle, cell_style_nowrap: ParagraphStyle,
                          header_style: ParagraphStyle, font_size: float) -> List[List[Paragraph]]:
        """Process table data and create Paragraph objects"""
        processed_data = []
        
        # Process header
        header_style.wordWrap = 'CJK'
        header_row = [Paragraph(str(cell), header_style) for cell in data[0]]
        processed_data.append(header_row)
        
        # Process data rows
        for row in data[1:]:
            processed_row = []
            for i, cell in enumerate(row):
                cell_content = str(cell)
                style = cell_style_nowrap if i in [0, 1] else cell_style_wrap
                
                if self.is_relatorio_separacao and i >= 2:
                    style = self._optimize_cell_style(cell_content, i, font_size, col_widths[i])
                    
                processed_row.append(Paragraph(cell_content, style))
            processed_data.append(processed_row)
            
        return processed_data

    def _optimize_cell_style(self, content: str, col_idx: int, font_size: float, col_width: float) -> ParagraphStyle:
        """Optimize cell style based on content and constraints"""
        style = ParagraphStyle(
            f'custom_{col_idx}',
            fontName='Helvetica',
            fontSize=font_size,
            alignment=1,
            leading=font_size * 1.2,
        )
        
        # Calculate if content fits without wrapping
        text_width = pdfmetrics.stringWidth(content, 'Helvetica', font_size)
        if text_width <= (col_width - 10):
            style.wordWrap = 'CJK'
        else:
            style.wordWrap = 'LTR'
            
        return style

    def _calculate_row_heights(self, processed_data: List[List[Paragraph]], 
                             col_widths: List[float], line_height_multiplier: float) -> List[float]:
        """Calculate optimal row heights"""
        row_heights = []
        for row in processed_data:
            max_height = 0
            for i, cell in enumerate(row):
                w, h = cell.wrap(col_widths[i] - 10, 1000)
                max_height = max(max_height, h)
            row_heights.append(max_height * line_height_multiplier)
        return row_heights

    def _apply_table_styles(self, table: Table, data: List[List[str]], 
                          font_size: float, header_font_size: float) -> None:
        """Apply styles to the table"""
        style = self.base_style
        
        # Add font sizes
        style.add('FONTSIZE', (0, 0), (-1, 0), header_font_size)
        style.add('FONTSIZE', (0, 1), (-1, -1), font_size)
        style.add('LEADING', (0, 0), (-1, 0), header_font_size * 1.2)
        style.add('LEADING', (0, 1), (-1, -1), font_size * 1.2)
        
        if self.is_relatorio_separacao:
            self._apply_separation_report_styles(style, data)
        else:
            self._apply_order_styles(style, data)
            
        table.setStyle(style)

    def _apply_separation_report_styles(self, style: TableStyle, data: List[List[str]]) -> None:
        """Apply styles specific to separation report"""
        for row_index, row in enumerate(data):
            bg_color = colors.white if row_index % 2 == 0 else colors.HexColor('#f0f0f0')
            style.add('BACKGROUND', (0, row_index), (-1, row_index), bg_color)
            
            if row_index > 0:
                try:
                    quantidade = int(row[1])
                    if quantidade > 1:
                        style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                except ValueError:
                    pass

    def _apply_order_styles(self, style: TableStyle, data: List[List[str]]) -> None:
        """Apply styles specific to order tables"""
        for row_index, row in enumerate(data):
            if row_index > 0:
                try:
                    quantidade = int(row[1])
                    if quantidade > 1:
                        style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                except ValueError:
                    pass

    def _create_minimum_size_table(self, data: List[List[str]], min_font_size: int,
                                 header_font_size: float, line_height_multiplier: float) -> Table:
        """Create table with minimum font size when optimal size cannot be found"""
        cell_style_wrap, cell_style_nowrap, header_style = self.create_styles(min_font_size, header_font_size)
        col_widths = self.calculate_column_widths(data, min_font_size, header_font_size)
        processed_data = self._process_table_data(data, col_widths, cell_style_wrap,
                                                cell_style_nowrap, header_style, min_font_size)
        row_heights = self._calculate_row_heights(processed_data, col_widths, line_height_multiplier)
        
        table = Table(processed_data, colWidths=col_widths, rowHeights=row_heights)
        self._apply_table_styles(table, data, min_font_size, header_font_size)
        return table
