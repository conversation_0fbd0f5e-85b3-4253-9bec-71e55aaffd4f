#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Processador de Pedidos Shopee
----------------------------
Este programa processa planilhas Excel com pedidos da Shopee e gera 
relatórios de separação e pedidos em PDF.

Execução:
    Apenas execute este script e selecione o arquivo Excel quando solicitado.
    Todas as dependências serão instaladas automaticamente.
"""

import sys
import os
import subprocess
import platform
from tkinter import messagebox
import tkinter as tk
from importlib.util import find_spec

# Lista de pacotes necessários com versões específicas para evitar problemas
REQUIRED_PACKAGES = {
    'pandas': 'pandas>=1.3.0',
    'reportlab': 'reportlab>=3.6.0',
    'PyMuPDF': 'PyMuPDF>=1.19.0',
    'Pillow': 'Pillow>=8.3.0',
    'openpyxl': 'openpyxl>=3.0.7',  # Para ler arquivos Excel
    'importlib-metadata': 'importlib-metadata>=4.6.0'
}

def show_message(title, message, is_error=False):
    """Função mantida apenas para compatibilidade, não exibe mais mensagens."""
    # Não faz nada, para evitar pop-ups
    pass

def run_pip_install(packages):
    """Executa pip install com os pacotes especificados silenciosamente."""
    # Determinando o executável Python correto (sys.executable)
    python_executable = sys.executable
    
    # Preparando comando para instalar pacotes
    if platform.system() == "Windows":
        cmd = [python_executable, "-m", "pip", "install", "--user"] + packages
    else:
        cmd = [python_executable, "-m", "pip", "install"] + packages

    try:
        # Executa o comando sem mostrar janela de terminal no Windows
        startupinfo = None
        if platform.system() == "Windows":
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0  # SW_HIDE
            
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            startupinfo=startupinfo
        )
        
        stdout, stderr = process.communicate()
        
        return process.returncode == 0
    except Exception:
        return False

def check_and_install_packages():
    """Verifica e instala pacotes necessários silenciosamente."""
    missing_packages = []
    
    # Verifica quais pacotes estão faltando
    for package_name, package_spec in REQUIRED_PACKAGES.items():
        if find_spec(package_name) is None:
            missing_packages.append(package_spec)
    
    # Se existirem pacotes faltando, tenta instalá-los silenciosamente
    if missing_packages:
        # Tenta instalar os pacotes sem mostrar pop-ups
        if not run_pip_install(missing_packages):
            # Se falhar, apenas registra no log e sai
            sys.exit(1)
    
    # Importa os pacotes após a instalação
    global pd, re, colors, A4, landscape, portrait, SimpleDocTemplate, Table, TableStyle, PageBreak, Paragraph
    global getSampleStyleSheet, ParagraphStyle, datetime, filedialog, canvas, pdfmetrics, io, fitz, Image, ImageReader
    
    # Importações necessárias
    try:
        import pandas as pd
        import re
        from reportlab.lib import colors
        from reportlab.lib.pagesizes import A4, landscape, portrait
        from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, PageBreak, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
        from datetime import datetime
        from tkinter import filedialog
        from reportlab.pdfgen import canvas
        from reportlab.pdfbase import pdfmetrics
        import io
        import fitz  # PyMuPDF
        from PIL import Image
        from reportlab.lib.utils import ImageReader
        
        print("Todas as importações foram concluídas com sucesso!")
        return True
    except ImportError as e:
        print(f"Falha ao importar as bibliotecas: {str(e)}")
        return False

# A partir daqui, o código original continua, mas só será executado se as dependências
# estiverem instaladas corretamente

# Função para selecionar um arquivo Excel usando uma caixa de diálogo
def selecionar_arquivo():
    root = tk.Tk()
    root.withdraw()  # Esconde a janela principal
    filename = filedialog.askopenfilename(title="Selecione a planilha Excel", 
                                         filetypes=[("Excel files", "*.xlsx *.xls")])
    return filename

# Função para extrair informações do produto de uma string formatada
def extrair_informacoes_produto(product_info):
    # Ajusta o padrão para permitir espaços extras e quebras de linha entre os elementos
    pattern = r"Product Name:(.*?); Variation Name:(.*?); Price:.*?; Quantity:\s*(\d+); SKU Reference No\.: (\d+)"
    # Remove quebras de linha e espaços desnecessários para facilitar a correspondência
    product_info = re.sub(r"\s+", " ", product_info.strip())
    matches = re.findall(pattern, product_info)

    produtos = []
    for match in matches:
        nome_produto = match[0].strip()
        variacao_produto = match[1].strip()
        quantidade = int(match[2].strip())
        sku = match[3].strip()
        produtos.append((sku, nome_produto, variacao_produto, quantidade))
    return produtos

# Função para gerar os dados do Relatório de Separação
def gerar_dados_relatorio_separacao(df):
    produtos = {}
    for info in df['product_info']:
        if pd.notna(info):  # Verifica se não é NaN
            dados_produtos = extrair_informacoes_produto(info)
            for sku, nome, variacao, qtd in dados_produtos:
                key = (sku, nome, variacao)
                if key in produtos:
                    produtos[key] += qtd
                else:
                    produtos[key] = qtd

    produtos_ordenados = sorted(produtos.items(), key=lambda item: item[0][0])

    data = [["SKU", "QNT", "NOME", "VARIAÇÃO"]]
    for (sku, nome, variacao), quantidade in produtos_ordenados:
        data.append([sku, quantidade, nome, variacao])

    return data

# Função para ajustar o estilo da tabela para o pedido
from reportlab.platypus import Table, TableStyle, Paragraph
from reportlab.lib import colors
from reportlab.lib.styles import ParagraphStyle

def ajustar_estilo_tabela_pedido(data, page_size, line_height_multiplier=1.2, max_font_size_override=12, min_font_size_override=8, is_relatorio_separacao=True):
    """
    Ajusta o estilo da tabela de pedidos, com tratamento especial para o relatório de separação:
    - No relatório de separação: tenta reduzir a fonte de elementos individuais para evitar quebra de linha
    - Nos pedidos individuais: permite quebra de linha normalmente
    """
    """
    Ajusta o estilo da tabela de pedidos, garantindo que:
    1. O texto NUNCA extrapole o grid
    2. Otimiza a largura das colunas com base no conteúdo
    3. Quebre linhas quando necessário (word wrap automático)
    4. Use tamanho de fonte adequado entre mínimo e máximo
    
    Args:
        data: Lista de listas com os dados da tabela
        page_size: Dimensões da página (largura, altura)
        line_height_multiplier: Multiplicador de altura da linha
        max_font_size_override: Tamanho máximo de fonte
        min_font_size_override: Tamanho mínimo de fonte
        
    Returns:
        Objeto Table com estilo aplicado
    """
    style = TableStyle([
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('LEFTPADDING', (0, 0), (-1, -1), 5),
        ('RIGHTPADDING', (0, 0), (-1, -1), 5),
    ])

    max_font_size = max_font_size_override
    min_font_size = min_font_size_override
    available_width = page_size[0] - 30
    available_height = page_size[1] - 40
    
    # Estilos para célula e cabeçalho
    def make_styles(font_size, header_font_size=None):
        if header_font_size is None:
            header_font_size = font_size

        # Estilo para células normais com quebra de linha
        cell_style_wrap = ParagraphStyle(
            name="cell_wrap",
            fontName="Helvetica",
            fontSize=font_size,
            alignment=1,  # center
            leading=font_size * 1.2,
        )
        
        # Estilo para células sem quebra de linha (SKU e QNT)
        cell_style_nowrap = ParagraphStyle(
            name="cell_nowrap",
            fontName="Helvetica",
            fontSize=font_size,
            alignment=1,  # center
            leading=font_size * 1.2,
            wordWrap='CJK'  # Previne quebra de linha
        )
        
        # Estilo para cabeçalhos
        header_style = ParagraphStyle(
            name="header",
            fontName="Helvetica-Bold",
            fontSize=header_font_size,
            alignment=1,
            leading=header_font_size * 1.2,
        )
        
        return cell_style_wrap, cell_style_nowrap, header_style
    
    # Determina a largura ideal para cada coluna com base no conteúdo e headers
    def calcular_larguras_colunas(font_size, header_font_size):
        # Calcula a largura mínima necessária para cada coluna baseada no conteúdo
        col_widths_content = [
            max(pdfmetrics.stringWidth(str(row[col]), 'Helvetica', font_size) for row in data[1:]) + 10
            for col in range(len(data[0]))
        ]
        
        # Calcula a largura mínima necessária para os headers
        col_widths_header = [
            pdfmetrics.stringWidth(str(data[0][col]), 'Helvetica-Bold', header_font_size) + 15
            for col in range(len(data[0]))
        ]
        
        # Usa o máximo entre a largura do conteúdo e do header para cada coluna
        col_widths_raw = [
            max(content, header)
            for content, header in zip(col_widths_content, col_widths_header)
        ]
        
        # Para SKU e QNT, garante que a largura seja exatamente a necessária (sem quebra)
        for col in [0, 1]:  # SKU e QNT são as colunas 0 e 1
            col_widths_raw[col] = max(
                max(pdfmetrics.stringWidth(str(row[col]), 'Helvetica', font_size) for row in data) + 10,
                col_widths_header[col]
            )
        
        # Calcula a proporção de cada coluna em relação ao total
        total_width_raw = sum(col_widths_raw)
        
        # Primeiro, garante as larguras mínimas baseadas nos headers
        min_widths = [
            pdfmetrics.stringWidth(str(data[0][col]), 'Helvetica-Bold', header_font_size) + 15
            for col in range(len(data[0]))
        ]
        
        # Adiciona espaço extra para a coluna VARIAÇÃO (normalmente a coluna 3)
        if len(min_widths) > 3:
            min_widths[3] += 20  # Adiciona 20 pontos extras para a coluna VARIAÇÃO
        
        # Ajusta as larguras proporcionalmente para caber na página
        col_widths = []
        space_used = 0
        
        # Primeiro, garante as larguras das colunas SKU e QNT
        for col in [0, 1]:
            width = max(col_widths_raw[col], min_widths[col])
            col_widths.append(width)
            space_used += width
        
        # Calcula o espaço restante para as outras colunas
        remaining_space = available_width - space_used
        remaining_min_width = sum(min_widths[2:])
        
        if remaining_min_width > remaining_space:
            # Se não houver espaço suficiente, usa as larguras mínimas
            col_widths.extend(min_widths[2:])
        else:
            # Distribui o espaço restante proporcionalmente, mas respeitando as larguras mínimas
            remaining_widths = col_widths_raw[2:]
            remaining_total = sum(remaining_widths)
            
            for col, (width, min_width) in enumerate(zip(remaining_widths, min_widths[2:]), 2):
                # Calcula a largura proporcional, mas garante o mínimo
                prop_width = (width / remaining_total) * remaining_space
                col_widths.append(max(prop_width, min_width))
        
        # Reajusta para garantir que a soma seja exatamente a largura disponível
        adjustment_factor = (available_width - col_widths[0] - col_widths[1]) / sum(col_widths[2:])
        col_widths[2:] = [width * adjustment_factor for width in col_widths[2:]]
        
        return col_widths

    # Define tamanhos de fonte padrão para o cabeçalho
    header_font_size = 12  # Tamanho fixo para headers
    
    # Tenta do maior para o menor tamanho de fonte
    for font_size in range(max_font_size, min_font_size - 1, -1):
        cell_style_wrap, cell_style_nowrap, header_style = make_styles(font_size, header_font_size)
        
        # Calcula larguras otimizadas para as colunas
        col_widths = calcular_larguras_colunas(font_size, header_font_size)
        
        processed_data = []
        # Processa o cabeçalho primeiro - evita quebra de linha em todos os headers
        header_style.wordWrap = 'CJK'  # Previne quebra de linha nos headers
        header_row = [Paragraph(str(cell), header_style) for cell in data[0]]
        processed_data.append(header_row)
        
        # Processa as linhas de dados
        for row_index, row in enumerate(data[1:]):
            processed_row = []
            for i, cell in enumerate(row):
                cell_content = str(cell)
                
                if is_relatorio_separacao and i >= 2:  # Para colunas NOME e VARIAÇÃO no relatório de separação
                    # Tenta encontrar o melhor tamanho de fonte para este elemento específico
                    cell_font_size = font_size
                    style_to_use = ParagraphStyle(
                        f'custom_{i}',
                        fontName='Helvetica',
                        fontSize=cell_font_size,
                        alignment=1,
                        leading=cell_font_size * 1.2,
                        wordWrap='CJK'  # Inicialmente tenta evitar quebra de linha
                    )
                    
                    # Tenta reduzir a fonte até caber em uma linha
                    while cell_font_size > min_font_size:
                        # Calcula a largura necessária com a fonte atual
                        text_width = pdfmetrics.stringWidth(cell_content, 'Helvetica', cell_font_size)
                        
                        # Se couber na coluna, usa esta fonte sem quebra de linha
                        if text_width <= (col_widths[i] - 10):
                            style_to_use.fontSize = cell_font_size
                            style_to_use.leading = cell_font_size * 1.2
                            style_to_use.wordWrap = 'CJK'  # Confirma que não haverá quebra de linha
                            break
                            
                        cell_font_size -= 0.5
                    
                    # Se mesmo com a menor fonte não couber, permite quebra de linha
                    if cell_font_size <= min_font_size:
                        style_to_use = ParagraphStyle(
                            f'custom_wrap_{i}',
                            fontName='Helvetica',
                            fontSize=min_font_size,  # Usa o menor tamanho de fonte permitido
                            alignment=1,
                            leading=min_font_size * 1.2,
                            wordWrap='LTR'  # Permite quebra de linha quando necessário
                        )
                else:
                    # SKU e QNT não têm quebra de linha
                    style_to_use = cell_style_nowrap if i in [0, 1] else cell_style_wrap
                    
                processed_row.append(Paragraph(cell_content, style_to_use))
            processed_data.append(processed_row)

        # Calcula altura de cada linha de acordo com o conteúdo
        row_heights = []
        for row in processed_data:
            max_height = 0
            for i, cell in enumerate(row):
                w, h = cell.wrap(col_widths[i] - 10, 1000)
                if h > max_height:
                    max_height = h
            row_heights.append(max_height * line_height_multiplier)

        table = Table(processed_data, colWidths=col_widths, rowHeights=row_heights)
        table.setStyle(style)
        table.setStyle(TableStyle([
            ('FONTSIZE', (0, 0), (-1, 0), header_font_size),  # Header font size
            ('FONTSIZE', (0, 1), (-1, -1), font_size),  # Content font size
            ('LEADING', (0, 0), (-1, 0), header_font_size * 1.2),  # Header leading
            ('LEADING', (0, 1), (-1, -1), font_size * 1.2),  # Content leading
        ]))

        w, h = table.wrapOn(None, page_size[0], page_size[1])
        if w <= available_width and h <= available_height:
            # Configura o estilo da tabela com base no tipo de relatório
            if is_relatorio_separacao:
                # Para o relatório de separação, adiciona cores alternadas nas linhas
                for row_index, row in enumerate(data):
                    bg_color = colors.white if row_index % 2 == 0 else colors.HexColor('#f0f0f0')
                    style.add('BACKGROUND', (0, row_index), (-1, row_index), bg_color)
                    
                    # Destaca quantidades maiores que 1
                    if row_index > 0:
                        try:
                            quantidade = int(row[1])
                            if quantidade > 1:
                                style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                        except ValueError:
                            pass
            else:
                # Para pedidos individuais, apenas destaca quantidades maiores que 1
                for row_index, row in enumerate(data):
                    if row_index > 0:
                        try:
                            quantidade = int(row[1])
                            if quantidade > 1:
                                style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                        except ValueError:
                            pass
                        
            table.setStyle(style)
            return table

    # Se não couber nem no menor tamanho, retorna com o menor possível
    cell_style_wrap, cell_style_nowrap, header_style = make_styles(min_font_size, header_font_size)
    col_widths = calcular_larguras_colunas(min_font_size, header_font_size)
    
    processed_data = []
    # Processa o cabeçalho primeiro
    header_style.wordWrap = 'CJK'  # Previne quebra de linha nos headers
    header_row = [Paragraph(str(cell), header_style) for cell in data[0]]
    processed_data.append(header_row)
    
    # Processa as linhas de dados
    for row in data[1:]:
        processed_row = []
        for i, cell in enumerate(row):
            cell_content = str(cell)
            
            if is_relatorio_separacao:  # Aplica lógica especial para o relatório de separação
                # Começa com o maior tamanho de fonte possível
                cell_font_size = min_font_size
                
                # Cria um estilo customizado para esta célula
                style_to_use = ParagraphStyle(
                    f'custom_{row_index}_{i}',  # Nome único para cada célula
                    fontName='Helvetica',
                    fontSize=cell_font_size,
                    alignment=1,
                    leading=cell_font_size * 1.2,
                )
                
                # Para SKU e QNT, nunca permite quebra de linha
                if i in [0, 1]:
                    style_to_use.wordWrap = 'CJK'  # Previne quebra de linha
                    
                    # Aumenta a fonte até não caber mais sem quebra
                    while cell_font_size < font_size:
                        next_size = cell_font_size + 0.5
                        text_width = pdfmetrics.stringWidth(cell_content, 'Helvetica', next_size)
                        if text_width > (col_widths[i] - 10):
                            break
                        cell_font_size = next_size
                        style_to_use.fontSize = cell_font_size
                        style_to_use.leading = cell_font_size * 1.2
                else:  # Para NOME e VARIAÇÃO
                    # Primeiro verifica se cabe sem quebra de linha com a fonte mínima
                    text_width = pdfmetrics.stringWidth(cell_content, 'Helvetica', min_font_size)
                    
                    if text_width <= (col_widths[i] - 10):  # Cabe sem quebra com fonte mínima
                        style_to_use.wordWrap = 'CJK'  # Previne quebra de linha
                        
                        # Aumenta a fonte até não caber mais sem quebra
                        while cell_font_size < font_size:
                            next_size = cell_font_size + 0.5
                            text_width = pdfmetrics.stringWidth(cell_content, 'Helvetica', next_size)
                            if text_width > (col_widths[i] - 10):
                                break
                            cell_font_size = next_size
                            style_to_use.fontSize = cell_font_size
                            style_to_use.leading = cell_font_size * 1.2
                    else:  # Não cabe sem quebra nem com fonte mínima
                        # Permite quebra de linha e usa a fonte padrão
                        style_to_use.fontSize = font_size
                        style_to_use.leading = font_size * 1.2
                        style_to_use.wordWrap = 'LTR'  # Permite quebra de linha
            else:  # Para pedidos individuais, mantém a lógica original
                # SKU e QNT não têm quebra de linha
                style_to_use = cell_style_nowrap if i in [0, 1] else cell_style_wrap
                
            processed_row.append(Paragraph(cell_content, style_to_use))
        processed_data.append(processed_row)
    
    row_heights = []
    for row in processed_data:
        max_height = 0
        for i, cell in enumerate(row):
            w, h = cell.wrap(col_widths[i] - 10, 1000)
            if h > max_height:
                max_height = h
        row_heights.append(max_height * line_height_multiplier)
    
    table = Table(processed_data, colWidths=col_widths, rowHeights=row_heights)
    
    # Configura o estilo da tabela com base no tipo de relatório
    if is_relatorio_separacao:
        # Para o relatório de separação, adiciona cores alternadas nas linhas
        for row_index, row in enumerate(data):
            bg_color = colors.white if row_index % 2 == 0 else colors.HexColor('#f0f0f0')
            style.add('BACKGROUND', (0, row_index), (-1, row_index), bg_color)
            
            # Destaca quantidades maiores que 1
            if row_index > 0:
                try:
                    quantidade = int(row[1])
                    if quantidade > 1:
                        style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                except ValueError:
                    pass
    else:
        # Para pedidos individuais, apenas destaca quantidades maiores que 1
        for row_index, row in enumerate(data):
            if row_index > 0:
                try:
                    quantidade = int(row[1])
                    if quantidade > 1:
                        style.add('BACKGROUND', (1, row_index), (1, row_index), colors.yellow)
                except ValueError:
                    pass
                
    table.setStyle(style)
    return table

# Função para gerar o Relatório de Separação em PDF
def gerar_pdf_relatorio_separacao(data, filename):
    pdf = SimpleDocTemplate(filename, pagesize=A4, topMargin=15, leftMargin=15, rightMargin=15, bottomMargin=15)
    elementos = []

    # Adiciona o título na parte superior do relatório de separação
    data_atual = datetime.today().strftime('%d/%m/%Y %H:%M')
    titulo = f"Separação Shopee {data_atual}"
    styles = getSampleStyleSheet()
    paragrafo_titulo = Paragraph(titulo, styles['Heading1'])
    elementos.append(paragrafo_titulo)

    # Divide os dados em páginas de 50 linhas (aumentado para aproveitar melhor o espaço)
    for i in range(0, len(data), 50):
        tabela_dados = data[i:i + 50]
        # Para o relatório de separação, usamos um multiplicador de altura da linha mais razoável
        tabela = ajustar_estilo_tabela_pedido(tabela_dados, A4, line_height_multiplier=1.2, is_relatorio_separacao=True)
        elementos.append(tabela)

        if i + 50 < len(data):
            elementos.append(PageBreak())

    pdf.build(elementos)

# Função para encontrar os produtos em cada pedido
def encontrar_produtos_por_pedido(df):
    pedidos = {}
    for index, row in df.iterrows():
        if pd.notna(row['product_info']):
            produtos = extrair_informacoes_produto(row['product_info'])
            order_sn = str(row['order_sn'])
            if order_sn not in pedidos:
                pedidos[order_sn] = []
            pedidos[order_sn].extend(produtos)
    return pedidos

# Função para gerar os dados dos pedidos em PDF
def gerar_pdf_dados(pedidos):
    styleSheet = getSampleStyleSheet()

    # Define um estilo personalizado para o título do pedido com fonte maior
    titulo_style = ParagraphStyle(name='TituloPedido', parent=styleSheet['Heading2'], fontSize=18)

    pedidos_buffers = []  # Lista para armazenar tuplas (buffer, orientação)

    for order_sn, produtos in pedidos.items():
        if not produtos:
            continue

        data = [["SKU", "QNT", "NOME", "VARIAÇÃO"]]
        for sku, nome, variacao, qtd in produtos:
            data.append([sku, qtd, nome, variacao])

        # Determina a orientação com base no número de itens
        if len(data) - 1 > 15:  # Exclui o cabeçalho
            page_size = A4  # Modo retrato
            orientation = 'portrait'
        else:
            page_size = landscape(A4)  # Modo paisagem
            orientation = 'landscape'

        # Ajusta a tabela com base no tamanho da página - com fonte maior para pedidos
        # Ajusta a tabela com base no tamanho da página - com fonte maior para pedidos e fonte mínima não tão pequena
        tabela = ajustar_estilo_tabela_pedido(data, page_size, line_height_multiplier=1.3, max_font_size_override=18, min_font_size_override=14, is_relatorio_separacao=False)

        elementos_pedido = []

        titulo_pedido = Paragraph(f"Código do Pedido: {order_sn}", titulo_style)
        elementos_pedido.append(titulo_pedido)
        elementos_pedido.append(tabela)
        elementos_pedido.append(PageBreak())

        buffer = io.BytesIO()
        pdf = SimpleDocTemplate(buffer, pagesize=page_size, topMargin=15, leftMargin=15, rightMargin=15, bottomMargin=15)
        pdf.build(elementos_pedido)
        buffer.seek(0)
        pedidos_buffers.append((buffer, orientation))

    return pedidos_buffers

# Função ajustada para gerar o PDF final com os pedidos e um PDF invertido se sobrar 1 ou 2 pedidos na última página
# Função gerar_imagens_pdf foi movida para uma única implementação abaixo

# Função principal
def main():
    # Cria a janela principal
    root = tk.Tk()
    root.title("Processador de Pedidos Shopee")
    root.geometry("600x420")
    root.resizable(False, False)
    
    # Variáveis para armazenar caminhos dos arquivos e opções
    arquivo_excel_var = tk.StringVar()
    arquivo_pdf_externo_var = tk.StringVar()
    status_var = tk.StringVar(value="Selecione o arquivo Excel e clique em Processar")
    separar_pdfs_var = tk.BooleanVar(value=False)

    # Frame principal
    main_frame = tk.Frame(root, padx=20, pady=20)
    main_frame.pack(fill="both", expand=True)

    # Título
    tk.Label(main_frame, text="Processador de Pedidos Shopee", font=("Arial", 14, "bold")).pack(pady=10)

    # Frame para seleção do Excel
    excel_frame = tk.LabelFrame(main_frame, text="Arquivo Excel de Pedidos", padx=10, pady=10)
    excel_frame.pack(fill="x", pady=5)
    excel_entry = tk.Entry(excel_frame, textvariable=arquivo_excel_var, width=50)
    excel_entry.pack(side="left", padx=5)
    
    def selecionar_excel():
        filename = filedialog.askopenfilename(
            title="Selecione a planilha Excel",
            filetypes=[("Excel files", "*.xlsx *.xls")]
        )
        if filename:
            arquivo_excel_var.set(filename)
            status_var.set("Excel selecionado. Clique em Processar.")
    
    tk.Button(excel_frame, text="Selecionar", command=selecionar_excel).pack(side="right")

    # Frame para seleção do PDF externo (opcional)
    pdf_frame = tk.LabelFrame(main_frame, text="PDF Externo (Opcional)", padx=10, pady=10)
    pdf_frame.pack(fill="x", pady=5)
    pdf_entry = tk.Entry(pdf_frame, textvariable=arquivo_pdf_externo_var, width=50)
    pdf_entry.pack(side="left", padx=5)
    
    def selecionar_pdf():
        filename = filedialog.askopenfilename(
            title="Selecione o PDF Externo (Opcional)",
            filetypes=[("PDF files", "*.pdf")]
        )
        if filename:
            arquivo_pdf_externo_var.set(filename)
            status_var.set("PDF externo selecionado. Clique em Processar.")
    
    tk.Button(pdf_frame, text="Selecionar", command=selecionar_pdf).pack(side="right")

    # Frame para opções
    opcoes_frame = tk.LabelFrame(main_frame, text="Opções", padx=10, pady=5)
    opcoes_frame.pack(fill="x", pady=5)
    
    # Checkbox para separar PDFs
    tk.Checkbutton(
        opcoes_frame,
        text="Separar PDF quando houver apenas 1 ou 2 pedidos na última página",
        variable=separar_pdfs_var
    ).pack(anchor="w", pady=5)
    
    # Status
    status_label = tk.Label(main_frame, textvariable=status_var)
    status_label.pack(pady=10)

    # Botão de processar - GARANTINDO QUE SEJA VISÍVEL
    def iniciar_processamento():
        processar_pedidos(
            arquivo_excel_var.get(),
            arquivo_pdf_externo_var.get(),
            status_var,
            root,
            separar_pdfs_var.get()
        )
    
    processar_btn = tk.Button(main_frame, text="PROCESSAR", command=iniciar_processamento, 
                             bg="#4CAF50", fg="white", font=("Arial", 10, "bold"),
                             height=4, width=20)
    processar_btn.pack(pady=15)

    # Copyright
    tk.Label(main_frame, text="© 2025 - Fernando R. - Processador de Pedidos Shopee", font=("Arial", 8)).pack(side="bottom")
    
    # Inicia o loop principal da interface
    root.mainloop()

def processar_pedidos(arquivo_excel, arquivo_pdf_externo, status_var, root, separar_pdfs=False):
    try:
        if not arquivo_excel:
            status_var.set("Selecione um arquivo Excel para processar.")
            return
        status_var.set("Processando arquivo Excel...")
        root.update()
        try:
            df = pd.read_excel(arquivo_excel)
        except FileNotFoundError:
            status_var.set("Erro: arquivo não encontrado")
            return
        except Exception as e:
            status_var.set(f"Erro ao ler o arquivo Excel: {str(e)}")
            return
        if 'product_info' not in df.columns or 'order_sn' not in df.columns:
            status_var.set("Erro: formato de arquivo incorreto")
            return
        output_dir = os.path.dirname(arquivo_excel) or os.getcwd()
        status_var.set("Gerando relatório de separação...")
        root.update()
        data_separacao = gerar_dados_relatorio_separacao(df)
        if not data_separacao or len(data_separacao) <= 1:
            status_var.set("Erro: dados insuficientes")
            return
        data_atual = datetime.today().strftime('%d%m%y-%H%M')
        nome_arquivo_separacao = os.path.join(output_dir, f"Separacao-{data_atual}.pdf")
        gerar_pdf_relatorio_separacao(data_separacao, nome_arquivo_separacao)
        status_var.set("Gerando relatório de pedidos...")
        root.update()
        pedidos = encontrar_produtos_por_pedido(df)
        if pedidos:
            pedidos_buffers = gerar_pdf_dados(pedidos)
            nome_arquivo_pedidos = os.path.join(output_dir, f"Pedidos-{data_atual}.pdf")
            
            # Verifica se tem PDF externo para combinar
            if arquivo_pdf_externo and os.path.exists(arquivo_pdf_externo):
                status_var.set("Combinando com PDF externo...")
                root.update()
                # Verifica se deve separar os PDFs
                if separar_pdfs:
                    # Usa a função específica para intercalar PDF externo (sem a última página) com o de pedidos
                    # (páginas ímpares do PDF externo, páginas pares do PDF de pedidos)
                    # A função gerar_imagens_pdf_com_externo_sem_ultima já não inclui a última página do PDF externo
                    # Gera o PDF principal e recebe os pedidos que precisam ser separados (se houver)
                    buffer_temp = io.BytesIO()
                    pedidos_separados = gerar_imagens_pdf(pedidos_buffers, buffer_temp, separar_ultimos=True)
                    
                    # Gera o PDF principal usando a função de intercalação sem a última página do PDF externo
                    gerar_imagens_pdf_com_externo_sem_ultima(pedidos_buffers[:-len(pedidos_separados)] if pedidos_separados else pedidos_buffers, 
                                                           nome_arquivo_pedidos, arquivo_pdf_externo)
                    
                    # Se tiver pedidos para separar, gera o PDF separado
                    if pedidos_separados:
                        nome_arquivo_pedidos_separados = os.path.join(output_dir, f"Pedidos-Separados-{data_atual}.pdf")
                        gerar_pdf_ultimos_pedidos(pedidos_separados, nome_arquivo_pedidos_separados, arquivo_pdf_externo)
                else:
                    # Usa a função específica para intercalar PDF externo com o de pedidos
                    # (páginas ímpares do PDF externo, páginas pares do PDF de pedidos)
                    gerar_imagens_pdf_com_externo(pedidos_buffers, nome_arquivo_pedidos, arquivo_pdf_externo)
            else:
                # Sem PDF externo
                if separar_pdfs:
                    # Gera o PDF principal e recebe os pedidos que precisam ser separados (se houver)
                    pedidos_separados = gerar_imagens_pdf(pedidos_buffers, nome_arquivo_pedidos, separar_ultimos=True)
                    
                    # Se tiver pedidos para separar, gera o PDF separado
                    if pedidos_separados:
                        nome_arquivo_pedidos_separados = os.path.join(output_dir, f"Pedidos-Separados-{data_atual}.pdf")
                        gerar_pdf_ultimos_pedidos(pedidos_separados, nome_arquivo_pedidos_separados)
                else:
                    # Gera o PDF normal sem separar
                    gerar_imagens_pdf(pedidos_buffers, nome_arquivo_pedidos, separar_ultimos=False)
            
            status_var.set(f"Processamento concluído! PDFs em: {output_dir}")
        else:
            status_var.set("Apenas relatório de separação gerado.")
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        status_var.set(f"Erro: {str(e)}")
        try:
            log_file = os.path.join(os.path.dirname(os.path.realpath(__file__)), "error_log.txt")
            with open(log_file, "w") as f:
                f.write(f"Data e hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Erro: {str(e)}\n\n")
                f.write(error_details)
        except:
            pass

def gerar_imagens_pdf(pedidos_buffers, output_path, separar_ultimos=False):
    A4_WIDTH, A4_HEIGHT = A4
    c = canvas.Canvas(output_path, pagesize=A4)
    total_pedidos = len(pedidos_buffers)
    pedidos_por_pagina = 4
    quadrante_width = A4_WIDTH / 2
    quadrante_height = A4_HEIGHT / 2
    positions_all = [
        (0, A4_HEIGHT / 2),
        (A4_WIDTH / 2, A4_HEIGHT / 2),
        (0, 0),
        (A4_WIDTH / 2, 0)
    ]
    
    # Calcula quantos pedidos ficarão na última página
    ultima_pagina_pedidos = len(pedidos_buffers) % 4
    if ultima_pagina_pedidos == 0:
        ultima_pagina_pedidos = 4 if len(pedidos_buffers) > 0 else 0
    
    # Só separa se tiver 1 ou 2 pedidos na última página
    pedidos_para_separar = []
    if separar_ultimos and ultima_pagina_pedidos <= 2 and ultima_pagina_pedidos > 0:
        # Remove os últimos pedidos do processamento principal
        pedidos_para_separar = pedidos_buffers[-ultima_pagina_pedidos:]
        pedidos_buffers = pedidos_buffers[:-ultima_pagina_pedidos]
        total_pedidos = len(pedidos_buffers)
    
    for i in range(0, total_pedidos, pedidos_por_pagina):
        buffers_orientations = pedidos_buffers[i:i+pedidos_por_pagina]
        num_pedidos_na_pagina = len(buffers_orientations)
        # Define a ordem dos índices conforme o número de pedidos
        if num_pedidos_na_pagina == 4:
            desired_order_indices = [2, 0, 3, 1]
            positions_indices = [0, 1, 2, 3]
        elif num_pedidos_na_pagina == 3:
            desired_order_indices = [2, 0, 1]
            positions_indices = [0, 1, 3]
        elif num_pedidos_na_pagina == 2:
            # Corrigido para seguir EXATAMENTE a mesma lógica das páginas com 3 ou 4 pedidos
            # Primeiro pedido na direita-superior (posição 1) e segundo na direita-inferior (posição 3)
            desired_order_indices = [0, 1]
            positions_indices = [1, 3]  # Posições direita-superior e direita-inferior
        elif num_pedidos_na_pagina == 1:
            # Corrigido para seguir EXATAMENTE a mesma lógica das páginas com 3 ou 4 pedidos
            # Primeiro pedido na direita-superior (posição 1)
            desired_order_indices = [0]
            positions_indices = [1]  # Posição direita-superior
        else:
            desired_order_indices = []
            positions_indices = []
        desired_order_indices = [idx for idx in desired_order_indices if idx < num_pedidos_na_pagina]
        positions_indices = positions_indices[:len(desired_order_indices)]
        buffers_to_draw = [buffers_orientations[idx] for idx in desired_order_indices]
        positions = [positions_all[idx] for idx in positions_indices]
        for (buffer, orientation), pos in zip(buffers_to_draw, positions):
            if buffer is None:
                continue
            doc = fitz.open(stream=buffer.getvalue(), filetype="pdf")
            page = doc.load_page(0)
            pix = page.get_pixmap()
            img_data = pix.tobytes("png")
            img = Image.open(io.BytesIO(img_data))
            if orientation == 'landscape':
                img = img.rotate(-90, expand=True)
            img_buffer = io.BytesIO()
            img.save(img_buffer, format="PNG")
            img_buffer.seek(0)
            c.drawImage(ImageReader(img_buffer), pos[0], pos[1], width=quadrante_width, height=quadrante_height)
        c.showPage()
    c.save()
    
    return pedidos_para_separar if pedidos_para_separar else []

def recortar_centralizar_metade_esquerda(pdf_doc, pagina_idx):
    """Recorta a metade esquerda de uma página de PDF e a centraliza.
    
    Args:
        pdf_doc: Documento PDF aberto (objeto fitz.Document)
        pagina_idx: Índice da página a ser manipulada
        
    Returns:
        Um novo documento PDF com a página manipulada
    """
    # Verifica se o índice da página é válido
    if pagina_idx < 0 or pagina_idx >= pdf_doc.page_count:
        return None
    
    # Cria um novo documento PDF para o resultado
    pdf_resultado = fitz.open()
    
    # Obtém a página a ser manipulada
    pagina = pdf_doc[pagina_idx]
    rect = pagina.rect
    
    # Calcula as dimensões da metade esquerda
    metade_width = rect.width / 2
    rect_esquerda = fitz.Rect(0, 0, metade_width, rect.height)
    
    # Cria uma nova página com o mesmo tamanho da original
    nova_pagina = pdf_resultado.new_page(width=rect.width, height=rect.height)
    
    # Calcula a posição centralizada para a metade esquerda
    # Queremos centralizar horizontalmente, então o x inicial será (largura_total - largura_metade) / 2
    x_centralizado = (rect.width - metade_width) / 2
    
    # Cria a matriz de transformação para recortar e centralizar
    matriz = fitz.Matrix(1, 0, 0, 1, x_centralizado, 0)
    
    # Desenha a metade esquerda da página original na nova página, na posição centralizada
    # Usamos o retângulo de destino (onde queremos colocar o conteúdo)
    destino_rect = fitz.Rect(x_centralizado, 0, x_centralizado + metade_width, rect.height)
    
    # Parâmetros corretos: (rect_destino, documento_fonte, num_pagina_fonte, clip=rect_origem)
    nova_pagina.show_pdf_page(destino_rect, pdf_doc, pagina_idx, clip=rect_esquerda)
    
    return pdf_resultado

def gerar_pdf_ultimos_pedidos(pedidos_para_separar, output_path, pdf_externo_path=None):
    """Gera um PDF apenas com os últimos pedidos (1 ou 2) e opcionalmente inclui a última página do PDF externo.
    A última página do PDF externo é colocada primeiro, seguida pelos pedidos separados."""
    if not pedidos_para_separar:
        return False
    
    # Usa os pedidos que foram separados
    ultimos_pedidos = pedidos_para_separar
    
    # Gera o PDF com os últimos pedidos centralizados
    buffer_ultimos = io.BytesIO()
    gerar_imagens_pdf_centralizados(ultimos_pedidos, buffer_ultimos)
    buffer_ultimos.seek(0)
    
    # Se não tiver PDF externo, salva diretamente
    if not pdf_externo_path:
        with open(output_path, 'wb') as f:
            f.write(buffer_ultimos.getvalue())
        return
    
    # Se tiver PDF externo, combina com a última página dele
    pdf_ultimos = fitz.open(stream=buffer_ultimos.getvalue(), filetype="pdf")
    pdf_externo = fitz.open(pdf_externo_path)
    pdf_resultado = fitz.open()
    
    # Manipula a última página do PDF externo (recorta metade esquerda e centraliza)
    if pdf_externo.page_count > 0:
        pagina_manipulada = recortar_centralizar_metade_esquerda(pdf_externo, pdf_externo.page_count-1)
        if pagina_manipulada:
            # Adiciona a página manipulada ao resultado
            pdf_resultado.insert_pdf(pagina_manipulada)
            pagina_manipulada.close()
    
    # Adiciona a página dos últimos pedidos DEPOIS
    pdf_resultado.insert_pdf(pdf_ultimos)
    
    # Salva o resultado
    pdf_resultado.save(output_path)
    
    # Fecha os PDFs
    pdf_ultimos.close()
    pdf_externo.close()
    pdf_resultado.close()

def gerar_imagens_pdf_centralizados(pedidos_buffers, output_path):
    """Gera um PDF com os pedidos centralizados horizontalmente, mantendo a posição vertical original."""
    A4_WIDTH, A4_HEIGHT = A4
    c = canvas.Canvas(output_path, pagesize=A4)
    total_pedidos = len(pedidos_buffers)
    quadrante_width = A4_WIDTH / 2
    quadrante_height = A4_HEIGHT / 2
    
    # Posições centralizadas horizontalmente para 1 ou 2 pedidos
    if total_pedidos == 1:
        # Um pedido na parte superior centralizado horizontalmente
        positions = [(A4_WIDTH/4, A4_HEIGHT/2)]  # Superior centralizado horizontalmente
    else:  # 2 pedidos
        # Dois pedidos centralizados horizontalmente, mantendo posições verticais originais
        positions = [
            (A4_WIDTH/4, A4_HEIGHT/2),  # Superior centralizado horizontalmente
            (A4_WIDTH/4, 0)              # Inferior centralizado horizontalmente
        ]
    
    for i, (buffer, orientation) in enumerate(pedidos_buffers):
        if i >= len(positions) or buffer is None:
            continue
            
        pos = positions[i]
        doc = fitz.open(stream=buffer.getvalue(), filetype="pdf")
        page = doc.load_page(0)
        pix = page.get_pixmap()
        img_data = pix.tobytes("png")
        img = Image.open(io.BytesIO(img_data))
        if orientation == 'landscape':
            img = img.rotate(-90, expand=True)
        img_buffer = io.BytesIO()
        img.save(img_buffer, format="PNG")
        img_buffer.seek(0)
        c.drawImage(ImageReader(img_buffer), pos[0], pos[1], width=quadrante_width, height=quadrante_height)
    
    c.showPage()
    c.save()
    return True


def gerar_imagens_pdf_com_externo(pedidos_buffers, output_path, pdf_externo_path):
    """Gera um PDF combinando os pedidos com um PDF externo para impressão frente e verso.
    O PDF externo será usado para páginas ímpares, os pedidos para páginas pares."""
    # Primeiro, gera o PDF dos pedidos em um buffer de memória
    buffer_pedidos = io.BytesIO()
    gerar_imagens_pdf(pedidos_buffers, buffer_pedidos)
    buffer_pedidos.seek(0)
    
    # Abre o PDF dos pedidos
    pdf_pedidos = fitz.open(stream=buffer_pedidos.getvalue(), filetype="pdf")
    
    # Abre o PDF externo
    pdf_externo = fitz.open(pdf_externo_path)
    
    # Cria um novo PDF para o resultado
    pdf_resultado = fitz.open()
    
    # Número de páginas necessárias para o resultado (máximo entre os dois PDFs)
    num_paginas = max(pdf_externo.page_count, pdf_pedidos.page_count * 2)
    
    # Alterna entre páginas do pedidos (pares) e externo (ímpares)
    # Na numeração física das páginas: 1=ímpar, 2=par, 3=ímpar, 4=par...
    # Mas nos índices do array: 0, 1, 2, 3...
    for i in range(num_paginas):
        if i % 2 == 0:  # Índices pares (0, 2, 4...) = Páginas físicas ímpares (1, 3, 5...) - PDF externo
            idx_externo = i // 2
            if idx_externo < pdf_externo.page_count:
                pdf_resultado.insert_pdf(pdf_externo, from_page=idx_externo, to_page=idx_externo)
            else:
                # Insere página em branco se não houver mais páginas no PDF externo
                pdf_resultado.new_page()
        else:  # Índices ímpares (1, 3, 5...) = Páginas físicas pares (2, 4, 6...) - PDF de pedidos
            idx_pedidos = i // 2
            if idx_pedidos < pdf_pedidos.page_count:
                pdf_resultado.insert_pdf(pdf_pedidos, from_page=idx_pedidos, to_page=idx_pedidos)
            else:
                # Insere página em branco se não houver mais pedidos
                pdf_resultado.new_page()
    
    # Salva o resultado
    pdf_resultado.save(output_path)
    
    # Fecha os PDFs
    pdf_pedidos.close()
    pdf_externo.close()
    pdf_resultado.close()

def gerar_imagens_pdf_com_externo_sem_ultima(pedidos_buffers, output_path, pdf_externo_path):
    """Gera um PDF combinando os pedidos com um PDF externo para impressão frente e verso,
    mas sem incluir a última página do PDF externo.
    O PDF externo será usado para páginas ímpares, os pedidos para páginas pares."""
    # Primeiro, gera o PDF dos pedidos em um buffer de memória
    buffer_pedidos = io.BytesIO()
    gerar_imagens_pdf(pedidos_buffers, buffer_pedidos)
    buffer_pedidos.seek(0)
    
    # Abre o PDF dos pedidos
    pdf_pedidos = fitz.open(stream=buffer_pedidos.getvalue(), filetype="pdf")
    
    # Abre o PDF externo
    pdf_externo = fitz.open(pdf_externo_path)
    
    # Cria um novo PDF para o resultado
    pdf_resultado = fitz.open()
    
    # Número de páginas do PDF externo (exceto a última)
    num_paginas_externo = max(0, pdf_externo.page_count - 1)
    
    # Número de páginas necessárias para o resultado
    num_paginas = max(num_paginas_externo * 2, pdf_pedidos.page_count * 2)
    
    # Alterna entre páginas do pedidos (pares) e externo (ímpares)
    # Na numeração física das páginas: 1=ímpar, 2=par, 3=ímpar, 4=par...
    # Mas nos índices do array: 0, 1, 2, 3...
    for i in range(num_paginas):
        if i % 2 == 0:  # Índices pares (0, 2, 4...) = Páginas físicas ímpares (1, 3, 5...) - PDF externo
            idx_externo = i // 2
            if idx_externo < num_paginas_externo:  # Não inclui a última página
                pdf_resultado.insert_pdf(pdf_externo, from_page=idx_externo, to_page=idx_externo)
            else:
                # Insere página em branco se não houver mais páginas no PDF externo
                pdf_resultado.new_page()
        else:  # Índices ímpares (1, 3, 5...) = Páginas físicas pares (2, 4, 6...) - PDF de pedidos
            idx_pedidos = i // 2
            if idx_pedidos < pdf_pedidos.page_count:
                pdf_resultado.insert_pdf(pdf_pedidos, from_page=idx_pedidos, to_page=idx_pedidos)
            else:
                # Insere página em branco se não houver mais pedidos
                pdf_resultado.new_page()
    
    # Salva o resultado
    pdf_resultado.save(output_path)
    
    # Fecha os PDFs
    pdf_pedidos.close()
    pdf_externo.close()
    pdf_resultado.close()

if __name__ == "__main__":
    # Verifica dependências silenciosamente e executa o programa principal
    check_and_install_packages()
    main()