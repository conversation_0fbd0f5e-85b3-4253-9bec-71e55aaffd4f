#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF utility functions for Shopee order processor
"""

import fitz
from typing import Optional

def crop_and_center_left_half(pdf_doc: fitz.Document, page_idx: int) -> Optional[fitz.Document]:
    """Crop the left half of a PDF page and center it

    Args:
        pdf_doc: Open PDF document
        page_idx: Index of the page to manipulate
        
    Returns:
        New PDF document with manipulated page or None if error
    """
    if page_idx < 0 or page_idx >= pdf_doc.page_count:
        return None
    
    # Create new PDF
    pdf_result = fitz.open()
    
    # Get page and its dimensions
    page = pdf_doc[page_idx]
    rect = page.rect
    
    # Calculate dimensions
    half_width = rect.width / 2
    left_rect = fitz.Rect(0, 0, half_width, rect.height)
    
    # Create new page
    new_page = pdf_result.new_page(width=rect.width, height=rect.height)
    
    # Calculate centered position
    x_centered = (rect.width - half_width) / 2
    dest_rect = fitz.Rect(x_centered, 0, x_centered + half_width, rect.height)
    
    # Show cropped and centered content
    new_page.show_pdf_page(dest_rect, pdf_doc, page_idx, clip=left_rect)
    
    return pdf_result
