#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
File utility functions for Shopee order processor
"""

import os
import sys
import subprocess
import platform
from importlib.util import find_spec
from typing import List, Dict, Optional
from config import Config

def check_and_install_packages() -> bool:
    """Check and install required packages silently"""
    missing_packages = _get_missing_packages()
    
    if missing_packages:
        return _install_packages(missing_packages)
        
    return True

def _get_missing_packages() -> List[str]:
    """Get list of missing required packages"""
    missing_packages = []
    for package_name, package_spec in Config.REQUIRED_PACKAGES.items():
        if find_spec(package_name) is None:
            missing_packages.append(package_spec)
    return missing_packages

def _install_packages(packages: List[str]) -> bool:
    """Install packages using pip"""
    python_executable = sys.executable
    
    cmd = [python_executable, "-m", "pip", "install"]
    if platform.system() == "Windows":
        cmd.append("--user")
    cmd.extend(packages)

    try:
        startupinfo = None
        if platform.system() == "Windows":
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = 0
            
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            startupinfo=startupinfo
        )
        
        stdout, stderr = process.communicate()
        return process.returncode == 0
    except Exception:
        return False

def log_error(error: Exception, error_details: str) -> None:
    """Log error details to file"""
    try:
        log_file = os.path.join(
            os.path.dirname(os.path.realpath(__file__)),
            "..",
            "error_log.txt"
        )
        with open(log_file, "w", encoding='utf-8') as f:
            from datetime import datetime
            f.write(f"Data e hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Erro: {str(error)}\n\n")
            f.write(error_details)
    except:
        pass  # Silently fail if unable to write log
