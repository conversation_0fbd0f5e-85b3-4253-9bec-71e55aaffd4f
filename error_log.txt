Data e hora: 2025-05-24 02:16:00
Erro: name 'header_font_size' is not defined

Traceback (most recent call last):
  File "Z:\Gerencia\fer\Programas e Scripts\shopee\main.py", line 537, in process_orders
    pdf_generator.generate_separation_report(separation_data, separation_file)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "Z:\Gerencia\fer\Programas e Scripts\shopee\services\pdf_generator.py", line 59, in generate_separation_report
    table = formatter.create_table(
        table_data,
        line_height_multiplier=self.config.TABLE_STYLES['line_height_multiplier']
    )
  File "Z:\Gerencia\fer\Programas e Scripts\shopee\services\table_formatter.py", line 187, in create_table
    self._apply_table_styles(table, data, font_size, header_font_size)
                                                     ^^^^^^^^^^^^^^^^
NameError: name 'header_font_size' is not defined. Did you mean: 'max_font_size'?
