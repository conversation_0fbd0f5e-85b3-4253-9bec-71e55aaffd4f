#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main application for Shopee Order Processor
"""

import os
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from tkinter.font import Font
import traceback
from pathlib import Path

from utils.file_utils import check_and_install_packages, log_error
from services.order_processor import OrderProcessor
from services.pdf_generator import PDFGenerator

# Cores do tema
COLORS = {
    'primary': '#2c3e50',
    'secondary': '#3498db',
    'success': '#2ecc71',
    'danger': '#e74c3c',
    'light': '#ecf0f1',
    'dark': '#2c3e50',
    'text': '#2c3e50',
    'background': '#f5f6fa'
}

# Estilos
STYLES = {
    'title': ('Segoe UI', 16, 'bold'),
    'subtitle': ('Segoe UI', 10),
    'button': ('Segoe UI', 10, 'bold'),
    'label': ('Segoe UI', 9),
    'status': ('Segoe UI', 9, 'italic')
}

class HoverButton(tk.Button):
    """Botão com efeito hover personalizado"""
    def __init__(self, master=None, **kw):
        self.default_bg = kw.pop('bg', COLORS['secondary'])
        self.hover_bg = kw.pop('hover_bg', '#2980b9')
        self.default_fg = kw.pop('fg', 'white')
        super().__init__(master, **kw)
        
        self['bg'] = self.default_bg
        self['fg'] = self.default_fg
        self['borderwidth'] = 0
        self['relief'] = 'flat'
        self['cursor'] = 'hand2'
        
        self.bind('<Enter>', self.on_enter)
        self.bind('<Leave>', self.on_leave)
    
    def on_enter(self, e):
        self['bg'] = self.hover_bg
    
    def on_leave(self, e):
        self['bg'] = self.default_bg

class ShopeeApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Processador de Pedidos Shopee")
        self.root.geometry("700x550")
        self.root.resizable(True, True)
        self.root.minsize(650, 500)
        self.root.configure(bg=COLORS['background'])
        
        # Configurar estilo
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configurar fonte padrão
        default_font = Font(family='Segoe UI', size=9)
        self.root.option_add('*Font', default_font)
        
        # Variables
        self.excel_path = tk.StringVar()
        self.external_pdf_path = tk.StringVar()
        self.status = tk.StringVar(value="Selecione o arquivo Excel e clique em Processar")
        self.separate_pdfs = tk.BooleanVar(value=False)
        
        # Carregar ícones
        self.load_icons()
        
        self.setup_ui()
    
    def load_icons(self):
        """Carrega os ícones usados na interface"""
        try:
            # Ícones base64 (simplificado para exemplo)
            self.excel_icon = '📊'  # Ícone unicode como fallback
            self.pdf_icon = '📄'    # Ícone unicode como fallback
        except Exception as e:
            # Fallback para texto caso os ícones não carreguem
            self.excel_icon = '[EXCEL]'
            self.pdf_icon = '[PDF]'

    def setup_ui(self):
        """Configura a interface do usuário"""
        # Container principal
        main_container = tk.Frame(self.root, bg=COLORS['background'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Frame de cabeçalho
        header_frame = tk.Frame(main_container, bg=COLORS['background'])
        header_frame.pack(fill='x', pady=(0, 20))
        
        # Título
        title_frame = tk.Frame(header_frame, bg=COLORS['background'])
        title_frame.pack(fill='x', pady=(0, 5))
        
        tk.Label(
            title_frame,
            text="Processador de Pedidos",
            font=STYLES['title'],
            fg=COLORS['primary'],
            bg=COLORS['background']
        ).pack(side='left')
        
        tk.Label(
            title_frame,
            text="Shopee",
            font=STYLES['title'],
            fg=COLORS['secondary'],
            bg=COLORS['background']
        ).pack(side='left', padx=5)
        
        # Subtítulo
        tk.Label(
            header_frame,
            text="Processe e gere relatórios de pedidos da Shopee",
            font=STYLES['subtitle'],
            fg=COLORS['dark'],
            bg=COLORS['background']
        ).pack(anchor='w')
        
        # Frame principal para conteúdo
        content_frame = tk.Frame(main_container, bg=COLORS['background'])
        content_frame.pack(fill='both', expand=True)
        
        # Frame para os campos de entrada
        input_frame = tk.Frame(content_frame, bg=COLORS['background'])
        input_frame.pack(fill='x', pady=(0, 15))

        # Excel file selection
        excel_frame = tk.LabelFrame(
            input_frame,
            text=" 1. Arquivo Excel de Pedidos ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=15,
            pady=10,
            bd=1,
            relief='groove'
        )
        excel_frame.pack(fill="x", pady=(0, 15), ipady=5)
        
        # Frame interno para entrada e botão
        excel_inner_frame = tk.Frame(excel_frame, bg=COLORS['background'])
        excel_inner_frame.pack(fill='x', expand=True)
        
        # Configurar estilo para os campos de entrada
        self.style.configure('TEntry',
                          padding=5,
                          relief='solid',
                          borderwidth=1,
                          fieldbackground='white',
                          foreground=COLORS['dark'])
        
        # Configurar estilo quando o campo estiver em foco
        self.style.map('TEntry',
                     fieldbackground=[('focus', 'white')],
                     foreground=[('focus', COLORS['dark'])])
        
        # Estilo para os campos de entrada
        entry_style = {
            'style': 'TEntry',
            'width': 50
        }
        
        # Frame para o campo de entrada com borda
        excel_entry_frame = tk.Frame(excel_inner_frame, bg='#bdc3c7', bd=1)
        excel_entry_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        # Campo de entrada dentro do frame
        excel_entry = ttk.Entry(
            excel_entry_frame,
            textvariable=self.excel_path,
            **entry_style
        )
        excel_entry.pack(fill='x', padx=1, pady=1)
        
        # Botão de seleção
        select_btn = HoverButton(
            excel_inner_frame,
            text=f" {self.excel_icon} Selecionar",
            command=self.select_excel,
            bg=COLORS['secondary'],
            fg='white',
            font=STYLES['button'],
            padx=15,
            pady=5
        )
        select_btn.pack(side='right')
        
        # Tooltip
        self.create_tooltip(
            excel_inner_frame,
            "Selecione o arquivo Excel com os pedidos da Shopee"
        )

        # External PDF selection
        pdf_frame = tk.LabelFrame(
            input_frame,
            text=" 2. PDF Externo (Opcional) ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=15,
            pady=10,
            bd=1,
            relief='groove'
        )
        pdf_frame.pack(fill="x", pady=(0, 15), ipady=5)
        
        # Frame interno para entrada e botão
        pdf_inner_frame = tk.Frame(pdf_frame, bg=COLORS['background'])
        pdf_inner_frame.pack(fill='x', expand=True)
        
        # Campo de entrada
        # Frame para o campo de entrada com borda
        pdf_entry_frame = tk.Frame(pdf_inner_frame, bg='#bdc3c7', bd=1)
        pdf_entry_frame.pack(side='left', fill='x', expand=True, padx=(0, 10))
        
        # Campo de entrada dentro do frame
        pdf_entry = ttk.Entry(
            pdf_entry_frame,
            textvariable=self.external_pdf_path,
            **entry_style
        )
        pdf_entry.pack(fill='x', padx=1, pady=1)
        
        # Botão de seleção
        pdf_btn = HoverButton(
            pdf_inner_frame,
            text=f" {self.pdf_icon} Selecionar",
            command=self.select_pdf,
            bg='#7f8c8d',
            fg='white',
            font=STYLES['button'],
            padx=15,
            pady=5
        )
        pdf_btn.pack(side='right')
        
        # Tooltip
        self.create_tooltip(
            pdf_inner_frame,
            "Selecione um PDF adicional para incluir nos relatórios (opcional)"
        )

        # Options
        options_frame = tk.LabelFrame(
            input_frame,
            text=" 3. Opções ",
            font=STYLES['label'],
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=15,
            pady=10,
            bd=1,
            relief='groove'
        )
        options_frame.pack(fill="x", pady=(0, 20), ipady=5)
        
        # Checkbox com estilo personalizado
        check_style = {
            'bg': COLORS['background'],
            'font': STYLES['label'],
            'selectcolor': COLORS['background'],
            'activebackground': COLORS['background'],
            'activeforeground': COLORS['dark'],
            'variable': self.separate_pdfs
        }
        
        check = tk.Checkbutton(
            options_frame,
            text="Separar PDF quando houver apenas 1 ou 2 pedidos na última página",
            **check_style
        )
        check.pack(anchor='w', pady=2)
        
        # Tooltip
        self.create_tooltip(
            check,
            "Ative esta opção para evitar páginas com poucos pedidos nos relatórios"
        )

        # Frame para o botão de processar
        button_frame = tk.Frame(content_frame, bg=COLORS['background'])
        button_frame.pack(fill='x', pady=10)
        
        # Frame para o log de saída (simplificado)
        log_frame = tk.Frame(button_frame, bg=COLORS['background'])
        log_frame.pack(fill='x', pady=(0, 10))
        
        # Área de texto para o log (simplificada)
        self.log_text = tk.Text(
            log_frame,
            height=2,
            wrap=tk.WORD,
            font=('Segoe UI', 8),
            bg=COLORS['background'],
            fg=COLORS['dark'],
            padx=5,
            pady=2,
            relief='flat',
            state='disabled',
            highlightthickness=1,
            highlightbackground='#ced4da',
            highlightcolor='#adb5bd'
        )
        self.log_text.pack(side='left', fill='x', expand=True)
        
        # Botão de copiar (menor e mais discreto)
        copy_btn = HoverButton(
            log_frame,
            text="⎘",
            command=self.copy_log_to_clipboard,
            bg=COLORS['background'],
            fg=COLORS['dark'],
            font=('Segoe UI', 7),
            width=1,
            height=1,
            hover_bg='#e9ecef',
            padx=2,
            pady=0,
            bd=0
        )
        copy_btn.pack(side='right', padx=(5, 0))
        
        # Botão de processar
        process_btn = HoverButton(
            button_frame,
            text="PROCESSAR PEDIDOS",
            command=self.process_orders,
            bg=COLORS['success'],
            fg='white',
            hover_bg='#27ae60',
            font=('Segoe UI', 11, 'bold'),
            padx=30,
            pady=12,
            bd=0,
            relief='flat',
            cursor='hand2'
        )
        process_btn.pack(fill='x')
        
        # Barra de status
        status_frame = tk.Frame(content_frame, bg=COLORS['background'])
        status_frame.pack(fill='x', side='bottom', pady=(10, 0))
        
        # Ícone de status
        self.status_icon = tk.Label(
            status_frame,
            text="⏳",
            font=('Segoe UI', 12),
            bg=COLORS['background'],
            fg=COLORS['dark']
        )
        self.status_icon.pack(side='left', padx=(0, 10))
        
        # Mensagem de status
        self.status_label = tk.Label(
            status_frame,
            textvariable=self.status,
            font=STYLES['status'],
            fg=COLORS['dark'],
            bg=COLORS['background'],
            wraplength=600,
            justify='left'
        )
        self.status_label.pack(side='left', fill='x', expand=True)
        
        # Barra de rodapé
        footer_frame = tk.Frame(self.root, bg=COLORS['primary'], height=30)
        footer_frame.pack(side='bottom', fill='x')
        
        # Copyright
        tk.Label(
            footer_frame,
            text="© 2025 - Fernando R. - Processador de Pedidos Shopee",
            font=('Segoe UI', 8),
            fg='white',
            bg=COLORS['primary']
        ).pack(side='right', padx=10, pady=5)
        
        # Configurar o grid para expansão
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(1, weight=1)
        content_frame.columnconfigure(0, weight=1)
    
    def create_tooltip(self, widget, text):
        """Cria uma dica de ferramenta para o widget"""
        def enter(event):
            x, y, _, _ = widget.bbox("insert")
            x += widget.winfo_rootx() + 25
            y += widget.winfo_rooty() + 25
            
            self.tooltip = tk.Toplevel(widget)
            self.tooltip.wm_overrideredirect(True)
            self.tooltip.wm_geometry(f"+{x}+{y}")
            
            label = tk.Label(
                self.tooltip,
                text=text,
                justify='left',
                background='#ffffe0',
                relief='solid',
                borderwidth=1,
                font=('Segoe UI', 8),
                wraplength=200
            )
            label.pack(ipadx=5, ipady=2)
        
        def leave(event):
            if hasattr(self, 'tooltip'):
                self.tooltip.destroy()
        
        widget.bind('<Enter>', enter)
        widget.bind('<Leave>', leave)

    def select_excel(self):
        """Handle Excel file selection"""
        filename = filedialog.askopenfilename(
            title="Selecione a planilha Excel",
            filetypes=[
                ("Arquivos Excel", "*.xlsx *.xls"),
                ("Todos os arquivos", "*.*")
            ],
        )
        if filename:
            self.excel_path.set(filename)
            msg = f"Arquivo selecionado: {os.path.basename(filename)}"
            self.update_log(msg, clear=True)
            self.update_status_icon("✅")

    def select_pdf(self):
        """Handle PDF file selection"""
        filename = filedialog.askopenfilename(
            title="Selecione o PDF Externo (Opcional)",
            filetypes=[
                ("Arquivos PDF", "*.pdf"),
                ("Todos os arquivos", "*.*")
            ],
        )
        if filename:
            self.external_pdf_path.set(filename)
            msg = f"PDF externo selecionado: {os.path.basename(filename)}"
            self.update_log(msg, clear=False)
            self.update_status_icon("✅")

    def update_status_icon(self, icon):
        """Atualiza o ícone de status"""
        self.status_icon.config(text=icon)
        
    def update_log(self, message, clear=False):
        """Atualiza o log de saída com uma única linha de texto"""
        # Remove quebras de linha e limita o tamanho do texto
        single_line = ' '.join(str(message).splitlines())[:100]  # Limita a 100 caracteres
        
        self.log_text.config(state='normal')
        if clear:
            self.log_text.delete(1.0, tk.END)
        else:
            self.log_text.delete(1.0, tk.END)  # Sempre limpa o conteúdo anterior
            
        self.log_text.insert(tk.END, single_line)
        self.log_text.see(tk.END)  # Rola para o final
        self.log_text.config(state='disabled')
        self.status.set(single_line)  # Atualiza a barra de status
    
    def copy_log_to_clipboard(self):
        """Copia o conteúdo do log para a área de transferência"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.log_text.get(1.0, tk.END))
            self.status.set("Log copiado para a área de transferência!")
            self.root.after(2000, lambda: self.status.set(""))  # Limpa a mensagem após 2 segundos
        except Exception as e:
            self.status.set(f"Erro ao copiar: {str(e)}")
    
    def process_orders(self):
        """Processa os pedidos e gera os PDFs"""
        try:
            # Validar entrada
            if not self.excel_path.get():
                error_msg = "❌ Erro: Selecione um arquivo Excel para processar."
                self.update_log(error_msg, clear=True)
                self.update_status_icon("❌")
                return

            self.update_log("⏳ Iniciando processamento do arquivo Excel...", clear=True)
            self.update_status_icon("⏳")
            self.root.update()

            # Inicializar processador e carregar dados
            try:
                processor = OrderProcessor(self.excel_path.get())
                processor.load_orders()
                processor.process_orders()
                
                # Obter caminhos de saída
                separation_file, orders_file, separated_orders_file = processor.generate_filenames()
                
                # Gerar relatório de separação
                self.status.set("⏳ Gerando relatório de separação...")
                self.root.update()
                
                separation_data = processor.get_separation_report_data()
                if not separation_data or len(separation_data) <= 1:
                    error_msg = "❌ Erro: Dados insuficientes no arquivo Excel"
                    self.update_log(error_msg)
                    self.update_status_icon("❌")
                    return
                
                # Inicializar gerador de PDF
                pdf_generator = PDFGenerator()
                
                # Gerar relatório de separação
                try:
                    pdf_generator.generate_separation_report(separation_data, separation_file)
                    success_msg = f"✅ Relatório de separação gerado com sucesso!\n   📄 {os.path.basename(separation_file)}"
                    self.update_log(success_msg)
                    self.update_status_icon("✅")
                except Exception as e:
                    self.status.set(f"❌ Erro ao gerar relatório de separação: {str(e)}")
                    self.update_status_icon("❌")
                    raise
                
                # Gerar PDF de pedidos
                self.update_log("⏳ Gerando relatório de pedidos...")
                self.root.update()
                
                if processor.orders:
                    try:
                        external_pdf = self.external_pdf_path.get() if (self.external_pdf_path.get() and 
                                                                     os.path.exists(self.external_pdf_path.get())) else None
                        
                        pdf_generator.generate_orders_pdf(
                            processor.orders,
                            orders_file,
                            external_pdf,
                            self.separate_pdfs.get()
                        )
                        
                        # Mostrar mensagem de sucesso com o caminho do arquivo
                        output_dir = os.path.dirname(orders_file)
                        success_msg = (
                            f"✅ Processamento concluído com sucesso!\n"
                            f"📂 Pasta de saída: {output_dir}\n"
                            f"📄 Relatórios gerados:\n"
                            f"   • {os.path.basename(separation_file)}\n"
                            f"   • {os.path.basename(orders_file)}"
                        )
                        self.update_log(success_msg)
                        self.update_status_icon("✅")
                        
                        # Abrir a pasta de saída
                        try:
                            if os.name == 'nt':  # Windows
                                os.startfile(output_dir)
                            elif os.name == 'posix':  # macOS e Linux
                                if os.uname().sysname == 'Darwin':
                                    os.system(f'open "{output_dir}"')
                                else:
                                    os.system(f'xdg-open "{output_dir}"')
                        except Exception as e:
                            self.update_log(f" Não foi possível abrir a pasta: {e}")
                            self.update_status_icon("")
                            
                    except Exception as e:
                        error_msg = f" Erro ao gerar relatório de pedidos: {str(e)}"
                        self.update_log(error_msg)
                        self.update_status_icon("")
                        raise
                else:
                    info_msg = "ℹ️ Nenhum pedido encontrado. Apenas o relatório de separação foi gerado."
                    self.update_log(info_msg)
                    self.update_status_icon("ℹ️")
                    
            except Exception as e:
                error_msg = f"❌ Erro ao processar o arquivo Excel: {str(e)}"
                self.update_log(error_msg)
                self.update_status_icon("❌")
                raise

        except Exception as e:
            error_details = traceback.format_exc()
            error_msg = f"❌ Ocorreu um erro inesperado: {str(e)}\n\nDetalhes:\n{error_details}"
            self.update_log(error_msg)
            self.update_status_icon("❌")
            log_error(e, error_details)
            
            # Mostrar mensagem de erro em uma caixa de diálogo
            messagebox.showerror(
                "Erro",
                f"Ocorreu um erro durante o processamento:\n\n{str(e)}\n\n"
                "Verifique o log para mais detalhes."
            )

def main():
    # Check dependencies
    if not check_and_install_packages():
        sys.exit(1)

    # Create and run application
    root = tk.Tk()
    app = ShopeeApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
