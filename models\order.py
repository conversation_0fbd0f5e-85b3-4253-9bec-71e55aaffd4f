#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Order model for Shopee order processing
"""

from dataclasses import dataclass
from typing import List, Tuple

@dataclass
class Product:
    sku: str
    name: str
    variation: str
    quantity: int

@dataclass
class Order:
    order_sn: str
    products: List[Product]

    @property
    def total_items(self) -> int:
        return sum(product.quantity for product in self.products)

    def to_table_data(self) -> List[List[str]]:
        """Convert order to table format for PDF generation"""
        data = [["SKU", "QNT", "NOME", "VARIAÇÃO"]]
        for product in self.products:
            data.append([
                product.sku,
                str(product.quantity),
                product.name,
                product.variation
            ])
        return data

    @classmethod
    def from_product_info(cls, order_sn: str, product_info: str) -> 'Order':
        """Create Order instance from raw product info string"""
        from services.order_processor import OrderProcessor  # Avoid circular import
        products = []
        for sku, name, variation, quantity in OrderProcessor.extract_product_info(product_info):
            products.append(Product(sku, name, variation, quantity))
        return cls(order_sn=order_sn, products=products)
