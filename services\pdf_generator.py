#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
PDF generation service for Shopee orders
"""

import io
import os
from typing import List, Tuple, Dict, Optional
from datetime import datetime
import fitz
from PIL import Image
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocTemplate, PageBreak, Paragraph
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader

from config import Config
from models.order import Order
from services.table_formatter import TableFormatter

class PDFGenerator:
    def __init__(self):
        self.a4_width, self.a4_height = A4
        self.config = Config()
        self.margins = self.config.PDF_MARGINS

    def generate_separation_report(self, data: List[List[str]], output_path: str) -> None:
        """Generate separation report PDF"""
        pdf = SimpleDocTemplate(
            output_path,
            pagesize=A4,
            topMargin=self.margins['top'],
            leftMargin=self.margins['left'],
            rightMargin=self.margins['right'],
            bottomMargin=self.margins['bottom']
        )
        elements = []

        # Add title
        data_atual = datetime.today().strftime('%d/%m/%Y %H:%M')
        titulo = f"Separação Shopee {data_atual}"
        styles = getSampleStyleSheet()
        titulo_style = styles['Heading1']
        elements.append(Paragraph(titulo, titulo_style))

        # Create tables for each page (50 rows per page)
        formatter = TableFormatter(A4, is_relatorio_separacao=True)
        for i in range(0, len(data), 50):
            table_data = data[i:i + 50]
            table = formatter.create_table(
                table_data,
                line_height_multiplier=self.config.TABLE_STYLES['line_height_multiplier']
            )
            elements.append(table)

            if i + 50 < len(data):
                elements.append(PageBreak())

        pdf.build(elements)

    def generate_orders_pdf(self, orders: Dict[str, Order], output_path: str,
                          external_pdf: Optional[str] = None, separate_last: bool = False) -> None:
        """
        Generate orders PDF with optional external PDF integration.
        
        Args:
            orders: Dictionary of Order objects to include in the PDF
            output_path: Path where the main PDF will be saved
            external_pdf: Optional path to an external PDF to include
            separate_last: If True, the last 1-2 orders will be moved to a separate PDF
        """
        # Generate PDF buffers for each order
        order_buffers = self._generate_order_buffers(orders)
        
        if not order_buffers:
            # No orders to process
            with fitz.open() as doc:
                doc.save(output_path)
            return
            
        if external_pdf:
            if separate_last:
                # This will handle both the main PDF and the separated PDF
                self._generate_combined_pdf_without_last(order_buffers, output_path, external_pdf)
            else:
                # Generate combined PDF with external PDF (all pages)
                self._generate_combined_pdf(order_buffers, output_path, external_pdf)
        else:
            # Generate orders PDF only (no external PDF)
            if separate_last:
                # Calculate how many orders to exclude (1 or 2)
                num_orders_to_exclude = len(order_buffers) % 4
                if num_orders_to_exclude == 0:
                    num_orders_to_exclude = 0  # No need to exclude any orders
                
                if num_orders_to_exclude > 0:
                    # Separate the last 1-2 orders
                    main_order_buffers = order_buffers[:-num_orders_to_exclude]
                    separated_order_buffers = order_buffers[-num_orders_to_exclude:]
                    
                    # Generate main PDF with remaining orders
                    if main_order_buffers:
                        self._generate_orders_pdf(main_order_buffers, output_path, False)
                    else:
                        # If no orders left for main PDF, create an empty one
                        with fitz.open() as doc:
                            doc.save(output_path)
                    
                    # Generate separated PDF with the last 1-2 orders
                    separated_path = output_path.replace('.pdf', '-separados.pdf')
                    self._generate_orders_pdf(separated_order_buffers, separated_path, False)
                else:
                    # No need to separate, generate as normal
                    self._generate_orders_pdf(order_buffers, output_path, False)
            else:
                # Generate all orders in a single PDF
                self._generate_orders_pdf(order_buffers, output_path, False)

    def _generate_order_buffers(self, orders: Dict[str, Order]) -> List[Tuple[io.BytesIO, str]]:
        """Generate PDF buffers for each order"""
        order_buffers = []
        styles = getSampleStyleSheet()
        titulo_style = ParagraphStyle(
            name='TituloPedido',
            parent=styles['Heading2'],
            fontSize=18
        )

        for order_sn, order in orders.items():
            # Skip empty orders
            if not order.products:
                continue

            # Determine page orientation based on number of products
            if len(order.products) > 15:
                page_size = A4
                orientation = 'portrait'
            else:
                page_size = landscape(A4)
                orientation = 'landscape'

            # Create PDF buffer
            buffer = io.BytesIO()
            pdf = SimpleDocTemplate(
                buffer,
                pagesize=page_size,
                topMargin=self.margins['top'],
                leftMargin=self.margins['left'],
                rightMargin=self.margins['right'],
                bottomMargin=self.margins['bottom']
            )

            # Generate order elements
            elements = []
            elements.append(Paragraph(f"Código do Pedido: {order_sn}", titulo_style))
            
            # Create table
            formatter = TableFormatter(page_size, is_relatorio_separacao=False)
            table = formatter.create_table(
                order.to_table_data(),
                line_height_multiplier=self.config.TABLE_STYLES['line_height_multiplier'],
                max_font_size=self.config.TABLE_STYLES['max_font_size'],
                min_font_size=self.config.TABLE_STYLES['min_font_size']
            )
            elements.append(table)
            elements.append(PageBreak())

            # Build PDF
            pdf.build(elements)
            buffer.seek(0)
            order_buffers.append((buffer, orientation))

        return order_buffers

    def _generate_orders_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                           output_path: str, separate_last: bool) -> None:
        """Generate PDF with order pages"""
        # Calculate positions for orders on page
        positions = [
            (0, self.a4_height / 2),
            (self.a4_width / 2, self.a4_height / 2),
            (0, 0),
            (self.a4_width / 2, 0)
        ]

        # Handle separation of last orders if needed
        if separate_last and len(order_buffers) % 4 <= 2 and len(order_buffers) > 0:
            main_buffers = order_buffers[:-len(order_buffers) % 4]
            separated_buffers = order_buffers[-len(order_buffers) % 4:]
        else:
            main_buffers = order_buffers
            separated_buffers = []

        # Generate main PDF
        c = canvas.Canvas(output_path, pagesize=A4)
        self._draw_orders_to_canvas(c, main_buffers, positions)
        c.save()

        # Generate separated orders if needed
        if separated_buffers:
            separated_path = output_path.replace('.pdf', '-separados.pdf')
            self._generate_separated_orders_pdf(separated_buffers, separated_path)

    def _draw_orders_to_canvas(self, c: canvas.Canvas, order_buffers: List[Tuple[io.BytesIO, str]],
                             positions: List[Tuple[float, float]], center: bool = False) -> None:
        """Draw orders on canvas in specified positions"""
        if not order_buffers:
            return
            
        quadrant_width = self.a4_width / 2
        quadrant_height = self.a4_height / 2

        # Se estiver no modo center, usar apenas a posição central
        if center and positions:
            center_pos = positions[0]
            positions = [
                center_pos,  # Centro
                (center_pos[0] + self.a4_width/2, center_pos[1]),  # Direita
                (center_pos[0], center_pos[1] - self.a4_height/2),  # Baixo
                (center_pos[0] + self.a4_width/2, center_pos[1] - self.a4_height/2)  # Diagonal
            ]


        for i in range(0, len(order_buffers), 4):
            page_buffers = order_buffers[i:i + 4]
            
            # Determine order and positions based on number of orders
            if len(page_buffers) == 4:
                order_indices = [2, 0, 3, 1]
                pos_indices = [0, 1, 2, 3]
            elif len(page_buffers) == 3:
                order_indices = [2, 0, 1]
                pos_indices = [0, 1, 2]  # Usar as primeiras 3 posições
            elif len(page_buffers) == 2:
                order_indices = [0, 1]
                pos_indices = [0, 2]  # Superior esquerdo e direito
            elif len(page_buffers) == 1:
                order_indices = [0]
                pos_indices = [0]  # Apenas a primeira posição
            else:
                continue

            # Draw orders in specified positions
            for idx, pos_idx in zip(order_indices, pos_indices):
                if idx >= len(page_buffers) or pos_idx >= len(positions):
                    continue

                buffer, orientation = page_buffers[idx]
                if buffer is None:
                    continue

                # Convert PDF to image
                try:
                    doc = fitz.open(stream=buffer.getvalue(), filetype="pdf")
                    page = doc.load_page(0)
                    pix = page.get_pixmap()
                    img_data = pix.tobytes("png")
                    img = Image.open(io.BytesIO(img_data))

                    # Rotate if landscape
                    if orientation == 'landscape':
                        img = img.rotate(-90, expand=True)

                    # Save as PNG
                    img_buffer = io.BytesIO()
                    img.save(img_buffer, format="PNG")
                    img_buffer.seek(0)


                    # Calculate position
                    pos = positions[pos_idx]
                    if center:
                        pos = (pos[0] + self.a4_width/4, pos[1])

                        
                    # Draw on canvas
                    c.drawImage(ImageReader(img_buffer), pos[0], pos[1],
                              width=quadrant_width, height=quadrant_height)
                              
                except Exception as e:
                    print(f"Erro ao processar pedido: {e}")
                    continue

            c.showPage()

    def _generate_combined_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                             output_path: str, external_pdf: str) -> None:
        """Generate PDF combining orders with external PDF"""
        # Generate orders PDF in memory
        orders_buffer = io.BytesIO()
        self._generate_orders_pdf(order_buffers, orders_buffer, False)
        orders_buffer.seek(0)

        # Open PDFs
        pdf_orders = fitz.open(stream=orders_buffer.getvalue(), filetype="pdf")
        pdf_external = fitz.open(external_pdf)
        pdf_result = fitz.open()

        # Combine PDFs alternating pages
        max_pages = max(pdf_external.page_count, pdf_orders.page_count * 2)
        for i in range(max_pages):
            if i % 2 == 0:  # External PDF pages (odd physical pages)
                idx_external = i // 2
                if idx_external < pdf_external.page_count:
                    pdf_result.insert_pdf(pdf_external, from_page=idx_external, to_page=idx_external)
                else:
                    pdf_result.new_page()
            else:  # Order pages (even physical pages)
                idx_orders = i // 2
                if idx_orders < pdf_orders.page_count:
                    pdf_result.insert_pdf(pdf_orders, from_page=idx_orders, to_page=idx_orders)
                else:
                    pdf_result.new_page()

        # Save and close
        pdf_result.save(output_path)
        pdf_orders.close()
        pdf_external.close()
        pdf_result.close()

    def _generate_combined_pdf_without_last(self, order_buffers: List[Tuple[io.BytesIO, str]],
                                          output_path: str, external_pdf: str) -> None:
        """
        Generate PDF combining orders with external PDF, excluding:
        - The last page of the external PDF
        - The last 1-2 orders (which will be moved to the separated PDF)
        """
        # Calculate how many orders to exclude (1 or 2)
        num_orders_to_exclude = len(order_buffers) % 4
        if num_orders_to_exclude == 0:
            num_orders_to_exclude = 0  # No need to exclude any orders
            
        # If we need to exclude orders, remove them from the main PDF
        main_order_buffers = order_buffers
        separated_order_buffers = []
        
        if num_orders_to_exclude > 0:
            main_order_buffers = order_buffers[:-num_orders_to_exclude]
            separated_order_buffers = order_buffers[-num_orders_to_exclude:]
            
            # Generate the separated PDF with the excluded orders
            separated_output_path = output_path.replace('.pdf', '-separados.pdf')
            self._generate_separated_orders_pdf(separated_order_buffers, separated_output_path, external_pdf)
        
        # Generate the main PDF with the remaining orders
        if main_order_buffers:
            # Generate orders PDF in memory with the remaining orders
            orders_buffer = io.BytesIO()
            self._generate_orders_pdf(main_order_buffers, orders_buffer, False)
            orders_buffer.seek(0)
            
            pdf_orders = fitz.open(stream=orders_buffer.getvalue(), filetype="pdf")
            pdf_external = fitz.open(external_pdf)
            pdf_result = fitz.open()
            
            try:
                # Exclude the last page of the external PDF
                num_external_pages = max(0, pdf_external.page_count - 1)
                max_pages = max(num_external_pages * 2, pdf_orders.page_count * 2)
                
                # Combine the PDFs, alternating between external and order pages
                for i in range(max_pages):
                    if i % 2 == 0:  # External PDF page
                        idx_external = i // 2
                        if idx_external < num_external_pages:
                            pdf_result.insert_pdf(pdf_external, from_page=idx_external, to_page=idx_external)
                        else:
                            pdf_result.new_page()
                    else:  # Order page
                        idx_orders = i // 2
                        if idx_orders < pdf_orders.page_count:
                            pdf_result.insert_pdf(pdf_orders, from_page=idx_orders, to_page=idx_orders)
                        else:
                            pdf_result.new_page()
                
                # Save the result
                pdf_result.save(output_path)
                
            finally:
                # Ensure all resources are properly closed
                pdf_orders.close()
                pdf_external.close()
                pdf_result.close()
        else:
            # If there are no orders left for the main PDF, create an empty PDF
            with fitz.open() as doc:
                doc.save(output_path)
                
            # Still generate the separated PDF if we have orders to separate
            if separated_order_buffers:
                self._generate_separated_orders_pdf(separated_order_buffers, 
                                                   output_path.replace('.pdf', '-separados.pdf'), 
                                                   external_pdf)

    def _generate_separated_orders_pdf(self, order_buffers: List[Tuple[io.BytesIO, str]],
                                     output_path: str, external_pdf: Optional[str] = None) -> None:
        """
        Generate PDF for separated orders, adjusting layout for 1 or 2 orders.
        - External PDF (Shipping Label): The left half of its last page is centered horizontally on a new A4 page.
        - Internal Orders PDF (Picking List/Declaration): Rendered orders (expected to be in the right half
          of a temporary A4 page by _draw_orders_to_canvas with center=True) are taken from this right half,
          and this clipped portion is centered horizontally on another new A4 page.
        The resulting PDF will have one page for the external PDF part (if provided) and one for orders (if provided).
        """
        pdf_result = fitz.open()  # This will be our final output PDF

        try:
            # Part 1: Process the external PDF (e.g., shipping label)
            if external_pdf and os.path.exists(external_pdf):
                try:
                    doc_ext = fitz.open(external_pdf)
                    if doc_ext.page_count > 0:
                        last_page_ext = doc_ext.load_page(doc_ext.page_count - 1)  # 0-indexed
                        
                        # Define the rectangle for the left half of the source external page
                        clip_rect_ext = fitz.Rect(0, 0, last_page_ext.rect.width / 2, last_page_ext.rect.height)
                        
                        page_result_ext = pdf_result.new_page(width=self.a4_width, height=self.a4_height)
                        
                        target_x_ext = (self.a4_width - clip_rect_ext.width) / 2
                        target_rect_ext = fitz.Rect(target_x_ext, 0, 
                                                  target_x_ext + clip_rect_ext.width, 
                                                  clip_rect_ext.height)

                        page_result_ext.show_pdf_page(target_rect_ext, doc_ext, last_page_ext.number, clip=clip_rect_ext)
                    doc_ext.close()
                except Exception as e:
                    print(f"Error processing external PDF for separated orders: {e}")
                    # Continue processing orders even if external PDF fails

            # Part 2: Process the internal order buffers (e.g., picking list)
            if order_buffers:
                try:
                    # Create a temporary PDF with the orders (this will be placed in the right half by _draw_orders_to_canvas)
                    temp_orders_buffer = io.BytesIO()
                    c = canvas.Canvas(temp_orders_buffer, pagesize=A4)
                    
                    # Draw orders in the right half of the page
                    self._draw_orders_to_canvas(
                        c, 
                        order_buffers, 
                        [(self.a4_width / 4, self.a4_height / 2)],  # This positions in the right half
                        center=True
                    )
                    c.save()
                    temp_orders_buffer.seek(0)
                    
                    # Open the temporary PDF and extract the right half
                    doc_orders_temp = fitz.open(stream=temp_orders_buffer.getvalue(), filetype="pdf")
                    if doc_orders_temp.page_count > 0:
                        first_page_orders_temp = doc_orders_temp.load_page(0)
                        
                        # Define the rectangle for the right half of the temporary page
                        clip_rect_orders = fitz.Rect(
                            first_page_orders_temp.rect.width / 2,  # Start from middle (right half)
                            0, 
                            first_page_orders_temp.rect.width,  # Full width of the right half
                            first_page_orders_temp.rect.height
                        )
                        
                        # Create a new page in the result PDF for the orders
                        page_result_orders = pdf_result.new_page(width=self.a4_width, height=self.a4_height)
                        
                        # Calculate position to center the clipped content
                        target_x_orders = (self.a4_width - clip_rect_orders.width) / 2
                        target_rect_orders = fitz.Rect(
                            target_x_orders, 
                            0,
                            target_x_orders + clip_rect_orders.width,
                            clip_rect_orders.height
                        )

                        # Draw the right half of the orders page, centered on the new page
                        page_result_orders.show_pdf_page(
                            target_rect_orders, 
                            doc_orders_temp, 
                            first_page_orders_temp.number, 
                            clip=clip_rect_orders
                        )
                    
                    doc_orders_temp.close()
                    temp_orders_buffer.close()
                except Exception as e:
                    print(f"Error processing order buffers for separated orders: {e}")
                    # Continue to save what we have even if there was an error with orders

            # Save the result if we have any pages
            if pdf_result.page_count > 0:
                pdf_result.save(output_path)
                print(f"Successfully saved separated orders PDF to: {output_path}")
            else:
                print(f"Warning: No pages were added to the separated PDF. Output file '{output_path}' might be empty or not created as expected.")
                
        except Exception as e:
            print(f"Unexpected error in _generate_separated_orders_pdf: {e}")
            raise
            
        finally:
            # Ensure all resources are properly closed
            pdf_result.close()
